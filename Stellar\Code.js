function extractLicensePlates() {
  const sheet = SpreadsheetApp.getActiveSpreadsheet().getActiveSheet();
  const dataRange = sheet.getRange("A:A"); // Отримати весь стовпець A
  const values = dataRange.getValues(); // Отримати всі значення зі стовпця A

  const vehiclePlates = [];
  const trailerPlates = [];

  // Оновлений регулярний вираз для номерних знаків
  // Використовується комбінований набір літер:
  // Офіційні кириличні літери (А, В, Е, І, К, М, Н, О, Р, С, Т, Х) та їх латинські аналоги,
  // а також F, G, Y, які зустрічаються у ваших даних.
  // Додано межі слова (\b) для запобігання помилковим збігам з частинами інших слів.
  const licensePlateRegex = /\b[АВЕІКМНОРСТХABEIKMHOPCTXFGY]{2}\s*\d{4}\s*[АВЕІКМНОРСТХABEIKMHOPCTXFGY]{2}\b/gi;

  for (let i = 0; i < values.length; i++) {
    const cellValue = values[i][0]; // Значення комірки в стовпці A
    let currentVehiclePlate = "";
    let currentTrailerPlate = "";

    if (typeof cellValue === 'string' && cellValue.trim() !== '') {
      const matches = [...cellValue.matchAll(licensePlateRegex)];
      
      if (matches.length >= 1) {
        currentVehiclePlate = matches[0][0].replace(/\s/g, '').toUpperCase(); // Видалити пробіли
      }
      if (matches.length >= 2) {
        currentTrailerPlate = matches[1][0].replace(/\s/g, '').toUpperCase(); // Видалити пробіли
      }
    }
    vehiclePlates.push([currentVehiclePlate]);
    trailerPlates.push([currentTrailerPlate]);
  }

  // Записати результати у стовпці B та C
  sheet.getRange(1, 2, vehiclePlates.length, 1).setValues(vehiclePlates);
  sheet.getRange(1, 3, trailerPlates.length, 1).setValues(trailerPlates);
}

function onOpen() {
  const ui = SpreadsheetApp.getUi();
  ui.createMenu('Витяг номерів')
      .addItem('Запустити витяг', 'extractLicensePlates')
      .addToUi();
}
