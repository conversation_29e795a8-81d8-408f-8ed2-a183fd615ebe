# System Patterns

**System Architecture:**
The system is implemented as a Google Apps Script, which is a cloud-based JavaScript platform for developing lightweight applications in the Google Workspace ecosystem. It operates directly within Google Sheets, leveraging its built-in functionalities.

**Key Technical Decisions:**
- **Google Apps Script Environment:** <PERSON>sen for direct integration with Google Sheets, ease of deployment, and accessibility for end-users without requiring external hosting.
- **Regular Expressions (Regex):** Utilized for robust pattern matching to identify and extract license plate numbers from varied text formats. This allows for flexibility in handling different character cases, Cyrillic/Latin equivalents, and optional spaces.
- **Contextual Keyword Matching:** Employed to differentiate between vehicle and trailer license plates. This involves searching for specific keywords (e.g., "а/м", "причіп") preceding the identified license plate patterns.
- **Sequential Matching Fallback:** In cases where explicit keywords are absent, a fallback mechanism assumes the first identified license plate is for the vehicle and the second for the trailer, based on common text patterns.

**Design Patterns in Use:**
- **Menu-driven Interface:** The `onOpen()` function creates a custom menu in Google Sheets, providing a simple and intuitive way for users to trigger the script.
- **Batch Processing:** The script reads all relevant data from column A into memory, processes it, and then writes all results back to columns B and C in a single batch operation (`setValues`). This is efficient for Google Apps Script as it minimizes calls to the Spreadsheet service, which can be slow.

**Component Relationships:**
- **Google Sheets:** The primary data source (column A) and destination (columns B and C).
- **Google Apps Script (`Code.js`):** Contains the core logic for data processing and interaction with Google Sheets.
    - `onOpen()`: Initializes the custom menu.
    - `extractLicensePlates()`: Contains the main logic for reading data, applying regex, extracting, and writing results.

**Critical Implementation Paths:**
1.  **Script Deployment:** The `Code.js` file must be deployed as a Google Apps Script project associated with a Google Sheet.
2.  **`onOpen()` Execution:** When the Google Sheet is opened, the `onOpen()` function automatically runs, creating the custom menu.
3.  **`extractLicensePlates()` Invocation:** The user triggers this function via the custom menu.
4.  **Data Reading:** The script reads all values from column A.
5.  **License Plate Extraction:** For each cell, the script:
    *   Applies the `licensePlateRegex` to find all potential license plates.
    *   Uses `vehicleIndicators` and `trailerIndicators` regex to find context-specific matches.
    *   Prioritizes matches with indicators.
    *   Falls back to sequential order if indicators are absent and multiple plates are found.
    *   Cleans extracted plates (removes spaces, converts to uppercase).
6.  **Data Writing:** The processed vehicle and trailer plates are written back to columns B and C respectively.
