# Product Context

**Why this project exists:**
This project exists to automate the extraction of vehicle and trailer license plate numbers from unstructured text data within Google Sheets. Manual extraction is time-consuming and prone to errors, especially with varied text formats.

**Problems it solves:**
- **Manual data entry errors:** Reduces human error in transcribing license plate numbers.
- **Time inefficiency:** Automates a repetitive task, freeing up user time.
- **Data inconsistency:** Ensures a standardized extraction process, leading to more consistent data.
- **Difficulty with varied formats:** Handles different text structures, case sensitivities, and character sets (Cyrillic/Latin) for license plates.

**How it should work:**
The script should run within Google Sheets. Upon execution, it should:
1. Read entries from a specified column ("Номенклатура товарів/послуг").
2. Identify and extract both vehicle and trailer license plate numbers using robust regular expressions.
3. Differentiate between vehicle and trailer plates based on surrounding keywords or their order of appearance.
4. Write the extracted vehicle plates to a designated column (B).
5. Write the extracted trailer plates to another designated column (C).
6. Provide a simple menu item in Google Sheets for easy execution.

**User experience goals:**
- **Simplicity:** The user should be able to run the script with a single click from a custom menu.
- **Accuracy:** The script should reliably extract correct license plate numbers, minimizing false positives or negatives.
- **Efficiency:** The extraction process should be fast, even for large datasets.
- **Clarity:** Results should be clearly presented in the designated columns.
