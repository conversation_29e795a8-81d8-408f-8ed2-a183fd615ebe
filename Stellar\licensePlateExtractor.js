/**
 * Український екстрактор номерних знаків - Standalone JavaScript модуль
 * Витягує номери автомобільних та причіпних номерних знаків з української транспортної документації
 */

/**
 * Перевіряє, чи містить текст англійські букви
 * @param {string} text - Текст для перевірки
 * @return {boolean} true, якщо містить англійські букви
 */
function hasEnglishLetters(text) {
  return /[A-Z]/i.test(text);
}

/**
 * Конвертує латинські букви в українські (кирилицю)
 * @param {string} text - Текст для конвертації
 * @return {string} Текст з українськими буквами
 */
function convertToUkrainian(text) {
  const latinToCyrillic = {
    'A': 'А', 'B': 'В', 'E': 'Е', 'K': 'К', 'M': 'М', 'H': 'Н', 
    'O': 'О', 'P': 'Р', 'C': 'С', 'T': 'Т', 'Y': 'У', 'X': 'Х'
  };
  
  let result = text.toUpperCase();
  for (const [latin, cyrillic] of Object.entries(latinToCyrillic)) {
    result = result.replace(new RegExp(latin, 'g'), cyrillic);
  }
  
  return result;
}

/**
 * Нормалізує номерний знак
 * @param {string} plate - Номерний знак для нормалізації
 * @return {string} Нормалізований номерний знак
 */
function normalizeLicensePlate(plate) {
  // Видаляємо пробіли та переводимо у верхній регістр
  let normalized = plate.replace(/\s+/g, '').toUpperCase();
  
  // Якщо немає англійських букв, конвертуємо в українські
  if (!hasEnglishLetters(normalized)) {
    normalized = convertToUkrainian(normalized);
  }
  
  return normalized;
}

/**
 * Перевіряє, чи є номерний знак дійсним українським форматом
 * @param {string} plate - Номерний знак для перевірки
 * @return {boolean} true, якщо номерний знак дійсний
 */
function isValidUkrainianPlate(plate) {
  // Український формат: 2 букви + 4 цифри + 2 букви
  // Букви можуть бути англійськими або українськими
  return /^[А-ЯІЇЄҐA-Z]{2}\d{4}[А-ЯІЇЄҐA-Z]{2}$/.test(plate);
}

/**
 * Перевіряє, чи не є номерний знак частиною документного номера
 * @param {string} plate - Номерний знак
 * @param {string} originalText - Оригінальний текст
 * @param {number} position - Позиція в тексті
 * @return {boolean} true, якщо це НЕ документний номер
 */
function isNotDocumentNumber(plate, originalText, position) {
  // Отримуємо контекст навколо номера
  const contextBefore = originalText.substring(Math.max(0, position - 20), position).toLowerCase();
  const contextAfter = originalText.substring(position + plate.length, position + plate.length + 20).toLowerCase();
  const fullContext = (contextBefore + ' ' + contextAfter).toLowerCase();
  
  // Виключаємо документні терміни
  const excludeTerms = [
    'ттн', 'тн', 'від', 'до', 'дата', 'номер', 'документ', 'рахунок', 'накладна',
    'договір', 'акт', 'протокол', 'довідка', 'посвідчення', 'сертифікат',
    'ліцензія', 'дозвіл', 'свідоцтво', 'паспорт', 'код', 'індекс', 'телефон',
    'факс', 'email', 'сайт', 'адреса', 'вулиця', 'будинок', 'квартира',
    'поштовий', 'банк', 'мфо', 'єдрпоу', 'інн', 'пдв', '№'
  ];
  
  // Перевіряємо, чи немає виключених термінів поруч
  for (const term of excludeTerms) {
    if (fullContext.includes(term)) {
      return false;
    }
  }
  
  // Перевіряємо, чи не є це частиною дати
  if (/\d{2}\.\d{2}\.\d{4}/.test(contextBefore + contextAfter)) {
    return false;
  }
  
  return true;
}

/**
 * Знаходить всі потенційні номерні знаки в тексті
 * @param {string} text - Текст для пошуку
 * @return {Array} Масив знайдених номерних знаків з позиціями
 */
function findAllLicensePlates(text) {
  const plates = [];
  
  // Шаблон для пошуку номерних знаків (з пробілами та без)
  // Формат: 2 букви + 4 цифри + 2 букви (з опціональними пробілами)
  const platePattern = /[А-ЯІЇЄҐA-Z]\s*[А-ЯІЇЄҐA-Z]\s*\d\s*\d\s*\d\s*\d\s*[А-ЯІЇЄҐA-Z]\s*[А-ЯІЇЄҐA-Z]/gi;
  
  let match;
  while ((match = platePattern.exec(text)) !== null) {
    const rawPlate = match[0];
    const normalizedPlate = normalizeLicensePlate(rawPlate);
    
    // Перевіряємо, чи це дійсний номерний знак
    if (isValidUkrainianPlate(normalizedPlate) && 
        isNotDocumentNumber(normalizedPlate, text, match.index)) {
      
      plates.push({
        plate: normalizedPlate,
        position: match.index,
        original: rawPlate
      });
    }
  }
  
  return plates;
}

/**
 * Основна функція для витягування номерних знаків
 * @param {string} text - Текст для обробки
 * @return {Object} Об'єкт з номерами автомобіля та причепа
 */
function extractLicensePlatesFromText(text) {
  const result = {
    vehicle: null,
    trailer: null
  };
  
  if (!text || typeof text !== 'string') {
    return result;
  }
  
  // Знаходимо всі номерні знаки
  const allPlates = findAllLicensePlates(text);
  
  if (allPlates.length === 0) {
    return result;
  }
  
  // Простий підхід: перший номер - автомобіль, другий - причіп
  if (allPlates.length >= 1) {
    result.vehicle = allPlates[0].plate;
  }
  
  if (allPlates.length >= 2) {
    result.trailer = allPlates[1].plate;
  }
  
  return result;
}

/**
 * Обробляє масив текстів та витягує номерні знаки
 * @param {Array<string>} textArray - Масив текстів для обробки
 * @return {Array<Object>} Масив результатів
 */
function processTextArray(textArray) {
  return textArray.map((text, index) => ({
    index: index + 1,
    text: text,
    result: extractLicensePlatesFromText(text)
  }));
}

// Експорт функцій для різних середовищ
if (typeof module !== 'undefined' && module.exports) {
  // Node.js середовище
  module.exports = {
    extractLicensePlatesFromText,
    processTextArray,
    normalizeLicensePlate,
    isValidUkrainianPlate,
    findAllLicensePlates
  };
} else if (typeof window !== 'undefined') {
  // Браузерне середовище
  window.LicensePlateExtractor = {
    extractLicensePlatesFromText,
    processTextArray,
    normalizeLicensePlate,
    isValidUkrainianPlate,
    findAllLicensePlates
  };
}
