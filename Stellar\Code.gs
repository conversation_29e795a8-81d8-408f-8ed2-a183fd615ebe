/**
 * Google Apps Script для витягування номерів автомобільних та причіпних номерних знаків
 * з української транспортної документації в Google Sheets
 */

/**
 * Основна функція для обробки електронної таблиці та витягування номерних знаків
 */
function extractLicensePlates() {
  const sheet = SpreadsheetApp.getActiveSheet();
  const dataRange = sheet.getDataRange();
  const values = dataRange.getValues();

  // Перевіряємо, чи є дані
  if (values.length <= 1) {
    Logger.log('Дані не знайдено або присутній лише рядок заголовків');
    return;
  }

  // Обробляємо кожен рядок, починаючи з рядка 2 (припускаємо, що рядок 1 - заголовки)
  for (let i = 1; i < values.length; i++) {
    const nomenclatureText = values[i][0]; // Колонка A

    if (!nomenclatureText || typeof nomenclatureText !== 'string') {
      continue;
    }

    // Витягуємо номерні знаки
    const extractedPlates = extractLicensePlatesFromText(nomenclatureText);

    // Записуємо номерний знак автомобіля в колонку B (індекс 1)
    if (extractedPlates.vehicle) {
      sheet.getRange(i + 1, 2).setValue(extractedPlates.vehicle);
    }

    // Записуємо номерний знак причепа в колонку C (індекс 2)
    if (extractedPlates.trailer) {
      sheet.getRange(i + 1, 3).setValue(extractedPlates.trailer);
    }
  }

  Logger.log('Витягування номерних знаків завершено');
}

/**
 * Нормалізація тексту для кращого виявлення номерних знаків
 * @param {string} text - Текст для нормалізації
 * @return {string} Нормалізований текст
 */
function normalizeTextForPlateDetection(text) {
  // Перетворюємо в верхній регістр для нечутливого до регістру пошуку
  let normalized = text.toUpperCase();

  // Мапа символів для поширених плутанин кирилиця/латиниця
  const charMap = {
    // Відображення кирилиці в латиницю
    'А': 'A', 'В': 'B', 'Е': 'E', 'К': 'K', 'М': 'M', 'Н': 'H', 'О': 'O', 'Р': 'P', 'С': 'C', 'Т': 'T', 'У': 'Y', 'Х': 'X',
    // Відображення латиниці в кирилицю (для українського контексту)
    'A': 'А', 'B': 'В', 'E': 'Е', 'K': 'К', 'M': 'М', 'H': 'Н', 'O': 'О', 'P': 'Р', 'C': 'С', 'T': 'Т', 'Y': 'У', 'X': 'Х'
  };

  // Застосовуємо нормалізацію символів (надаємо перевагу кирилиці для українських номерів)
  for (const [latin, cyrillic] of Object.entries(charMap)) {
    if (latin.length === 1 && cyrillic.length === 1 && latin.charCodeAt(0) < 128) {
      normalized = normalized.replace(new RegExp(latin, 'g'), cyrillic);
    }
  }

  return normalized;
}

/**
 * Перевірка, чи є потенційний номерний знак дійсним
 * @param {string} plate - Потенційний номерний знак
 * @param {string} originalText - Оригінальний текст
 * @param {number} position - Позиція знайденого збігу в тексті
 * @return {boolean} true, якщо номерний знак дійсний
 */
function isValidLicensePlate(plate, originalText, position) {
  // Перевіряємо базовий формат
  if (!/^[А-ЯІЇЄҐ]{2}\d{4}[А-ЯІЇЄҐ]{2}$/.test(plate)) {
    return false;
  }

  // Отримуємо контекст навколо знайденого збігу
  const contextBefore = originalText.substring(Math.max(0, position - 30), position).toLowerCase();
  const contextAfter = originalText.substring(position + plate.length, position + plate.length + 30).toLowerCase();
  const fullContext = (contextBefore + ' ' + contextAfter).toLowerCase();

  // Виключаємо збіги поруч з документними термінами
  const excludeTerms = [
    'ттн', 'тн', 'від', 'до', 'дата', 'номер', 'документ', 'рахунок', 'накладна',
    'договір', 'акт', 'протокол', 'довідка', 'посвідчення', 'сертифікат',
    'ліцензія', 'дозвіл', 'свідоцтво', 'паспорт', 'код', 'індекс', 'телефон',
    'факс', 'email', 'сайт', 'адреса', 'вулиця', 'будинок', 'квартира',
    'поштовий', 'банк', 'рахунок', 'мфо', 'єдрпоу', 'інн', 'пдв', '№', 'р573'
  ];

  // Перевіряємо, чи не знаходиться номерний знак поруч з виключеними термінами
  for (const term of excludeTerms) {
    if (fullContext.includes(term)) {
      return false;
    }
  }

  // Перевіряємо, чи не є це частиною дати (формат DD.MM.YYYY)
  if (/\d{2}\.\d{2}\.\d{4}/.test(contextBefore + contextAfter)) {
    return false;
  }

  // Перевіряємо, чи не є це частиною телефонного номера
  if (/[\+\d\(\)\-\s]{10,}/.test(contextBefore + contextAfter)) {
    return false;
  }

  return true;
}

/**
 * Витягування номерних знаків з тексту
 * @param {string} text - Текст для пошуку номерних знаків
 * @return {Object} Об'єкт, що містить номерні знаки автомобіля та причепа
 */
function extractLicensePlatesFromText(text) {
  const result = {
    vehicle: null,
    trailer: null
  };

  // Нормалізуємо текст для кращого виявлення
  const normalizedText = normalizeTextForPlateDetection(text);

  // Покращені шаблони українських номерних знаків з межами слів
  // Шаблон 1: Стандартний формат (без пробілів) з межами слів
  const standardPattern = /\b[А-ЯІЇЄҐA-Z]{2}\d{4}[А-ЯІЇЄҐA-Z]{2}\b/gi;

  // Шаблон 2: Формат з обмеженими пробілами (максимум 1 пробіл між символами)
  const spacedPattern = /\b[А-ЯІЇЄҐA-Z]\s?[А-ЯІЇЄҐA-Z]\s?\d\s?\d\s?\d\s?\d\s?[А-ЯІЇЄҐA-Z]\s?[А-ЯІЇЄҐA-Z]\b/gi;

  // Знаходимо всі потенційні номерні знаки
  let allPlates = [];
  let platePositions = [];

  // Пошук стандартних номерних знаків
  let match;
  const standardRegex = new RegExp(standardPattern.source, standardPattern.flags);
  while ((match = standardRegex.exec(normalizedText)) !== null) {
    const plate = normalizeTextForPlateDetection(match[0]);
    if (isValidLicensePlate(plate, text, match.index)) {
      allPlates.push(plate);
      platePositions.push(match.index);
    }
  }

  // Пошук номерних знаків з пробілами
  const spacedRegex = new RegExp(spacedPattern.source, spacedPattern.flags);
  while ((match = spacedRegex.exec(text)) !== null) {
    const plate = normalizeTextForPlateDetection(match[0].replace(/\s+/g, ''));
    if (isValidLicensePlate(plate, text, match.index)) {
      allPlates.push(plate);
      platePositions.push(match.index);
    }
  }

  // Видаляємо дублікати
  const uniquePlates = [];
  const uniquePositions = [];
  for (let i = 0; i < allPlates.length; i++) {
    if (!uniquePlates.includes(allPlates[i])) {
      uniquePlates.push(allPlates[i]);
      uniquePositions.push(platePositions[i]);
    }
  }

  allPlates = uniquePlates;
  platePositions = uniquePositions;

  if (allPlates.length === 0) {
    return result;
  }
  
  // Спеціальний шаблон для формату "номер1 / номер2" (автомобіль / причіп)
  const combinedPattern = /([А-ЯІЇЄҐA-Z]{2}\s?\d\s?\d\s?\d\s?\d\s?[А-ЯІЇЄҐA-Z]{2})\s*\/\s*([А-ЯІЇЄҐA-Z]{2}\s?\d\s?\d\s?\d\s?\d\s?[А-ЯІЇЄҐA-Z]{2})/gi;

  // Перевіряємо спеціальний формат "номер1 / номер2"
  let match = combinedPattern.exec(text);
  if (match && match[1] && match[2]) {
    const vehiclePlate = normalizeTextForPlateDetection(match[1].replace(/\s+/g, ''));
    const trailerPlate = normalizeTextForPlateDetection(match[2].replace(/\s+/g, ''));

    // Перевіряємо, чи обидва номери дійсні
    if (isValidLicensePlate(vehiclePlate, text, match.index) &&
        isValidLicensePlate(trailerPlate, text, match.index + match[1].length)) {
      result.vehicle = vehiclePlate;
      result.trailer = trailerPlate;
      return result;
    }
  }

  // Контекстні шаблони для ідентифікації номерних знаків автомобілів та причепів з підтримкою пробілів
  const vehicleContexts = [
    /а\/м\s+[А-ЯІЇЄҐA-Z\s]*([А-ЯІЇЄҐA-Z]{2}\s?\d\s?\d\s?\d\s?\d\s?[А-ЯІЇЄҐA-Z]{2})/gi,
    /автомобіль\s+[А-ЯІЇЄҐA-Z\s]*([А-ЯІЇЄҐA-Z]{2}\s?\d\s?\d\s?\d\s?\d\s?[А-ЯІЇЄҐA-Z]{2})/gi,
    /RENAULT\s+([А-ЯІЇЄҐA-Z]{2}\s?\d\s?\d\s?\d\s?\d\s?[А-ЯІЇЄҐA-Z]{2})/gi,
    /РЕНО\s+([А-ЯІЇЄҐA-Z]{2}\s?\d\s?\d\s?\d\s?\d\s?[А-ЯІЇЄҐA-Z]{2})/gi,
    /VOLVO\s+([А-ЯІЇЄҐA-Z]{2}\s?\d\s?\d\s?\d\s?\d\s?[А-ЯІЇЄҐA-Z]{2})/gi,
    /SCANIA\s+([А-ЯІЇЄҐA-Z]{2}\s?\d\s?\d\s?\d\s?\d\s?[А-ЯІЇЄҐA-Z]{2})/gi,
    /MAN\s+([А-ЯІЇЄҐA-Z]{2}\s?\d\s?\d\s?\d\s?\d\s?[А-ЯІЇЄҐA-Z]{2})/gi,
    /DAF\s+([А-ЯІЇЄҐA-Z]{2}\s?\d\s?\d\s?\d\s?\d\s?[А-ЯІЇЄҐA-Z]{2})/gi,
    /MERCEDES\s+([А-ЯІЇЄҐA-Z]{2}\s?\d\s?\d\s?\d\s?\d\s?[А-ЯІЇЄҐA-Z]{2})/gi,
    /IVECO\s+([А-ЯІЇЄҐA-Z]{2}\s?\d\s?\d\s?\d\s?\d\s?[А-ЯІЇЄҐA-Z]{2})/gi
  ];

  const trailerContexts = [
    /н\/пр\.?\s*([А-ЯІЇЄҐA-Z]{2}\s?\d\s?\d\s?\d\s?\d\s?[А-ЯІЇЄҐA-Z]{2})/gi,
    /напівпричіп\s+([А-ЯІЇЄҐA-Z]{2}\s?\d\s?\d\s?\d\s?\d\s?[А-ЯІЇЄҐA-Z]{2})/gi,
    /причіп\s+([А-ЯІЇЄҐA-Z]{2}\s?\d\s?\d\s?\d\s?\d\s?[А-ЯІЇЄҐA-Z]{2})/gi,
    /прицеп\s+([А-ЯІЇЄҐA-Z]{2}\s?\d\s?\d\s?\d\s?\d\s?[А-ЯІЇЄҐA-Z]{2})/gi
  ];

  // Намагаємося ідентифікувати номерні знаки автомобілів за контекстом
  for (const pattern of vehicleContexts) {
    let match;
    while ((match = pattern.exec(text)) !== null) {
      const plate = normalizeTextForPlateDetection(match[1].replace(/\s+/g, ''));
      if (isValidLicensePlate(plate, text, match.index)) {
        result.vehicle = plate;
        break;
      }
    }
    if (result.vehicle) break;
  }

  // Намагаємося ідентифікувати номерні знаки причепів за контекстом
  for (const pattern of trailerContexts) {
    let match;
    while ((match = pattern.exec(text)) !== null) {
      const plate = normalizeTextForPlateDetection(match[1].replace(/\s+/g, ''));
      if (isValidLicensePlate(plate, text, match.index)) {
        result.trailer = plate;
        break;
      }
    }
    if (result.trailer) break;
  }

  // Якщо не вдалося ідентифікувати за контекстом, використовуємо логіку на основі позиції
  // але тільки якщо знайдені номерні знаки пройшли всі перевірки
  if (!result.vehicle && !result.trailer && allPlates.length > 0) {
    // Якщо у нас кілька номерних знаків, припускаємо, що перший - автомобіль, другий - причіп
    if (allPlates.length >= 2) {
      result.vehicle = allPlates[0];
      result.trailer = allPlates[1];
    } else {
      // Один номерний знак - намагаємося визначити за навколишнім текстом
      const plateIndex = platePositions[0];
      const beforePlate = text.substring(Math.max(0, plateIndex - 20), plateIndex).toLowerCase();
      const afterPlate = text.substring(plateIndex + allPlates[0].length, plateIndex + allPlates[0].length + 20).toLowerCase();

      if (beforePlate.includes('н/пр') || beforePlate.includes('напівпричіп') || beforePlate.includes('причіп') || beforePlate.includes('прицеп')) {
        result.trailer = allPlates[0];
      } else {
        result.vehicle = allPlates[0];
      }
    }
  }

  return result;
}

/**
 * Функція для налаштування заголовків колонок
 */
function setupHeaders() {
  const sheet = SpreadsheetApp.getActiveSheet();

  // Встановлюємо заголовки
  sheet.getRange(1, 1).setValue('Номенклатура товарів/послуг');
  sheet.getRange(1, 2).setValue('Номер автомобіля');
  sheet.getRange(1, 3).setValue('Номер причепа');

  // Форматуємо заголовки
  const headerRange = sheet.getRange(1, 1, 1, 3);
  headerRange.setFontWeight('bold');
  headerRange.setBackground('#e6f3ff');

  Logger.log('Заголовки успішно налаштовано');
}

/**
 * Функція для очищення витягнутих даних (колонки B та C)
 */
function clearExtractedData() {
  const sheet = SpreadsheetApp.getActiveSheet();
  const lastRow = sheet.getLastRow();

  if (lastRow > 1) {
    // Очищуємо колонки B та C, починаючи з рядка 2
    sheet.getRange(2, 2, lastRow - 1, 2).clearContent();
    Logger.log('Витягнуті дані очищено');
  }
}

/**
 * Тестова функція з прикладами даних, включаючи граничні випадки
 */
function testExtraction() {
  const testTexts = [
    // Оригінальні тестові випадки
    "Перевезення вантажу за маршрутом Вільногірськ – Степанки, а/м RENAULT AE5887КІ, н/пр. АЕ0234ХК, водій Нежинський М. ТТН 0555 від 07.01.24р.",
    "Транспортна послуга у міжнародному сполученні Степанки (Україна) - Вільнюс (Литва) Строгий СВ6395ВХ СВ2061ХО",

    // Граничний випадок: Конкретний пропущений номерний знак
    "Транспортування вантажу автомобілем СА9075ВН",

    // Граничний випадок: Літери в нижньому регістрі
    "а/м VOLVO са9075вн, н/пр. ае0234хк",

    // Граничний випадок: Літери з пробілами
    "Автомобіль С А 9075 В Н, причіп А Е 0234 Х К",

    // Граничний випадок: Змішані кирилиця/латиниця
    "а/м SCANIA CA9075BH, н/пр. AE0234XK",

    // Граничний випадок: Різні пробіли
    "Перевезення а/м  С А9075 ВН  н/пр.  А Е0234ХК",

    // Граничний випадок: Змішаний регістр з пробілами
    "транспорт с а 9075 в н та а е 0234 х к",

    // Граничний випадок: Формат з пробілами та слешем
    "Транспортні послуги смт. Малинівка - с. Степанки , РЕНО ВН 6524 НР / ВО 1518 XF, ттн № Р573 від 21.03.2024р.",

    // Граничний випадок: Текст з ТТН (має повернути null)
    "Транспортні послуги за маршрутом м.Вільногірськ - с.Степанки ттн 1037 від 29.02.2024"
  ];

  testTexts.forEach((text, index) => {
    Logger.log(`Тест ${index + 1}: ${text}`);
    const result = extractLicensePlatesFromText(text);
    Logger.log(`Автомобіль: ${result.vehicle}, Причіп: ${result.trailer}`);
    Logger.log('---');
  });
}
