/**
 * Google Apps Script для витягування номерів автомобільних та причіпних номерних знаків
 * з української транспортної документації в Google Sheets
 */

/**
 * Основна функція для обробки електронної таблиці та витягування номерних знаків
 */
function extractLicensePlates() {
  const sheet = SpreadsheetApp.getActiveSheet();
  const dataRange = sheet.getDataRange();
  const values = dataRange.getValues();

  // Перевіряємо, чи є дані
  if (values.length <= 1) {
    Logger.log('Дані не знайдено або присутній лише рядок заголовків');
    return;
  }

  // Обробляємо кожен рядок, починаючи з рядка 2 (припускаємо, що рядок 1 - заголовки)
  for (let i = 1; i < values.length; i++) {
    const nomenclatureText = values[i][0]; // Колонка A

    if (!nomenclatureText || typeof nomenclatureText !== 'string') {
      continue;
    }

    // Витягуємо номерні знаки
    const extractedPlates = extractLicensePlatesFromText(nomenclatureText);

    // Записуємо номерний знак автомобіля в колонку B (індекс 1)
    if (extractedPlates.vehicle) {
      sheet.getRange(i + 1, 2).setValue(extractedPlates.vehicle);
    }

    // Записуємо номерний знак причепа в колонку C (індекс 2)
    if (extractedPlates.trailer) {
      sheet.getRange(i + 1, 3).setValue(extractedPlates.trailer);
    }
  }

  Logger.log('Витягування номерних знаків завершено');
}

/**
 * Нормалізація тексту для кращого виявлення номерних знаків
 * @param {string} text - Текст для нормалізації
 * @return {string} Нормалізований текст
 */
function normalizeTextForPlateDetection(text) {
  // Перетворюємо в верхній регістр для нечутливого до регістру пошуку
  let normalized = text.toUpperCase();

  // Мапа символів для поширених плутанин кирилиця/латиниця
  const charMap = {
    // Відображення кирилиці в латиницю
    'А': 'A', 'В': 'B', 'Е': 'E', 'К': 'K', 'М': 'M', 'Н': 'H', 'О': 'O', 'Р': 'P', 'С': 'C', 'Т': 'T', 'У': 'Y', 'Х': 'X',
    // Відображення латиниці в кирилицю (для українського контексту)
    'A': 'А', 'B': 'В', 'E': 'Е', 'K': 'К', 'M': 'М', 'H': 'Н', 'O': 'О', 'P': 'Р', 'C': 'С', 'T': 'Т', 'Y': 'У', 'X': 'Х'
  };

  // Застосовуємо нормалізацію символів (надаємо перевагу кирилиці для українських номерів)
  for (const [latin, cyrillic] of Object.entries(charMap)) {
    if (latin.length === 1 && cyrillic.length === 1 && latin.charCodeAt(0) < 128) {
      normalized = normalized.replace(new RegExp(latin, 'g'), cyrillic);
    }
  }

  return normalized;
}

/**
 * Витягування номерних знаків з тексту
 * @param {string} text - Текст для пошуку номерних знаків
 * @return {Object} Об'єкт, що містить номерні знаки автомобіля та причепа
 */
function extractLicensePlatesFromText(text) {
  const result = {
    vehicle: null,
    trailer: null
  };

  // Нормалізуємо текст для кращого виявлення
  const normalizedText = normalizeTextForPlateDetection(text);

  // Покращені шаблони українських номерних знаків
  // Шаблон 1: Стандартний формат (без пробілів)
  const standardPattern = /[А-ЯІЇЄҐA-Z]{2}\d{4}[А-ЯІЇЄҐA-Z]{2}/gi;

  // Шаблон 2: Формат з пробілами між символами
  const spacedPattern = /[А-ЯІЇЄҐA-Z]\s*[А-ЯІЇЄҐA-Z]\s*\d\s*\d\s*\d\s*\d\s*[А-ЯІЇЄҐA-Z]\s*[А-ЯІЇЄҐA-Z]/gi;

  // Шаблон 3: Змішаний формат з опціональними пробілами та змішаним регістром
  const flexiblePattern = /[А-ЯІЇЄҐa-zA-Z]\s*[А-ЯІЇЄҐa-zA-Z]\s*\d\s*\d\s*\d\s*\d\s*[А-ЯІЇЄҐa-zA-Z]\s*[А-ЯІЇЄҐa-zA-Z]/gi;

  // Знаходимо всі номерні знаки, використовуючи різні шаблони
  let allPlates = [];

  // Спочатку пробуємо стандартний шаблон на нормалізованому тексті
  const standardMatches = normalizedText.match(standardPattern) || [];
  allPlates = allPlates.concat(standardMatches);

  // Пробуємо шаблон з пробілами на оригінальному тексті (щоб зберегти оригінальні пробіли)
  const spacedMatches = text.match(spacedPattern) || [];
  const normalizedSpacedMatches = spacedMatches.map(plate =>
    normalizeTextForPlateDetection(plate.replace(/\s+/g, ''))
  );
  allPlates = allPlates.concat(normalizedSpacedMatches);

  // Пробуємо гнучкий шаблон на оригінальному тексті
  const flexibleMatches = text.match(flexiblePattern) || [];
  const normalizedFlexibleMatches = flexibleMatches.map(plate =>
    normalizeTextForPlateDetection(plate.replace(/\s+/g, ''))
  );
  allPlates = allPlates.concat(normalizedFlexibleMatches);

  // Видаляємо дублікати та фільтруємо дійсні номерні знаки
  allPlates = [...new Set(allPlates)].filter(plate => {
    // Переконуємося, що номерний знак відповідає точному українському формату після нормалізації
    return /^[А-ЯІЇЄҐ]{2}\d{4}[А-ЯІЇЄҐ]{2}$/.test(plate);
  });

  if (allPlates.length === 0) {
    return result;
  }
  
  // Контекстні шаблони для ідентифікації номерних знаків автомобілів та причепів
  const vehicleContexts = [
    /а\/м\s+[А-ЯІЇЄҐA-Z\s]*([А-ЯІЇЄҐA-Z]{2}\d{4}[А-ЯІЇЄҐA-Z]{2})/gi,
    /автомобіль\s+[А-ЯІЇЄҐA-Z\s]*([А-ЯІЇЄҐA-Z]{2}\d{4}[А-ЯІЇЄҐA-Z]{2})/gi,
    /RENAULT\s+([А-ЯІЇЄҐA-Z]{2}\d{4}[А-ЯІЇЄҐA-Z]{2})/gi,
    /VOLVO\s+([А-ЯІЇЄҐA-Z]{2}\d{4}[А-ЯІЇЄҐA-Z]{2})/gi,
    /SCANIA\s+([А-ЯІЇЄҐA-Z]{2}\d{4}[А-ЯІЇЄҐA-Z]{2})/gi,
    /MAN\s+([А-ЯІЇЄҐA-Z]{2}\d{4}[А-ЯІЇЄҐA-Z]{2})/gi
  ];

  const trailerContexts = [
    /н\/пр\.?\s*([А-ЯІЇЄҐA-Z]{2}\d{4}[А-ЯІЇЄҐA-Z]{2})/gi,
    /напівпричіп\s+([А-ЯІЇЄҐA-Z]{2}\d{4}[А-ЯІЇЄҐA-Z]{2})/gi,
    /причіп\s+([А-ЯІЇЄҐA-Z]{2}\d{4}[А-ЯІЇЄҐA-Z]{2})/gi
  ];

  // Намагаємося ідентифікувати номерні знаки автомобілів за контекстом
  for (const pattern of vehicleContexts) {
    const match = pattern.exec(text);
    if (match && match[1]) {
      result.vehicle = normalizeTextForPlateDetection(match[1]);
      break;
    }
  }

  // Намагаємося ідентифікувати номерні знаки причепів за контекстом
  for (const pattern of trailerContexts) {
    const match = pattern.exec(text);
    if (match && match[1]) {
      result.trailer = normalizeTextForPlateDetection(match[1]);
      break;
    }
  }
  
  // Якщо не вдалося ідентифікувати за контекстом, використовуємо логіку на основі позиції
  if (!result.vehicle && !result.trailer && allPlates.length > 0) {
    // Якщо у нас кілька номерних знаків, припускаємо, що перший - автомобіль, другий - причіп
    if (allPlates.length >= 2) {
      result.vehicle = allPlates[0];
      result.trailer = allPlates[1];
    } else {
      // Один номерний знак - намагаємося визначити за навколишнім текстом
      const plateIndex = text.indexOf(allPlates[0]);
      const beforePlate = text.substring(Math.max(0, plateIndex - 20), plateIndex).toLowerCase();
      const afterPlate = text.substring(plateIndex + allPlates[0].length, plateIndex + allPlates[0].length + 20).toLowerCase();

      if (beforePlate.includes('н/пр') || beforePlate.includes('напівпричіп') || beforePlate.includes('причіп')) {
        result.trailer = allPlates[0];
      } else {
        result.vehicle = allPlates[0];
      }
    }
  }

  return result;
}

/**
 * Функція для налаштування заголовків колонок
 */
function setupHeaders() {
  const sheet = SpreadsheetApp.getActiveSheet();

  // Встановлюємо заголовки
  sheet.getRange(1, 1).setValue('Номенклатура товарів/послуг');
  sheet.getRange(1, 2).setValue('Номер автомобіля');
  sheet.getRange(1, 3).setValue('Номер причепа');

  // Форматуємо заголовки
  const headerRange = sheet.getRange(1, 1, 1, 3);
  headerRange.setFontWeight('bold');
  headerRange.setBackground('#e6f3ff');

  Logger.log('Заголовки успішно налаштовано');
}

/**
 * Функція для очищення витягнутих даних (колонки B та C)
 */
function clearExtractedData() {
  const sheet = SpreadsheetApp.getActiveSheet();
  const lastRow = sheet.getLastRow();

  if (lastRow > 1) {
    // Очищуємо колонки B та C, починаючи з рядка 2
    sheet.getRange(2, 2, lastRow - 1, 2).clearContent();
    Logger.log('Витягнуті дані очищено');
  }
}

/**
 * Тестова функція з прикладами даних, включаючи граничні випадки
 */
function testExtraction() {
  const testTexts = [
    // Оригінальні тестові випадки
    "Перевезення вантажу за маршрутом Вільногірськ – Степанки, а/м RENAULT AE5887КІ, н/пр. АЕ0234ХК, водій Нежинський М. ТТН 0555 від 07.01.24р.",
    "Транспортна послуга у міжнародному сполученні Степанки (Україна) - Вільнюс (Литва) Строгий СВ6395ВХ СВ2061ХО",

    // Граничний випадок: Конкретний пропущений номерний знак
    "Транспортування вантажу автомобілем СА9075ВН",

    // Граничний випадок: Літери в нижньому регістрі
    "а/м VOLVO са9075вн, н/пр. ае0234хк",

    // Граничний випадок: Літери з пробілами
    "Автомобіль С А 9075 В Н, причіп А Е 0234 Х К",

    // Граничний випадок: Змішані кирилиця/латиниця
    "а/м SCANIA CA9075BH, н/пр. AE0234XK",

    // Граничний випадок: Різні пробіли
    "Перевезення а/м  С А9075 ВН  н/пр.  А Е0234ХК",

    // Граничний випадок: Змішаний регістр з пробілами
    "транспорт с а 9075 в н та а е 0234 х к"
  ];

  testTexts.forEach((text, index) => {
    Logger.log(`Тест ${index + 1}: ${text}`);
    const result = extractLicensePlatesFromText(text);
    Logger.log(`Автомобіль: ${result.vehicle}, Причіп: ${result.trailer}`);
    Logger.log('---');
  });
}
