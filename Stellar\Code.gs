/**
 * Google Apps Script for extracting vehicle and trailer license plate numbers
 * from Ukrainian transport documentation in Google Sheets
 */

/**
 * Main function to process the spreadsheet and extract license plate numbers
 */
function extractLicensePlates() {
  const sheet = SpreadsheetApp.getActiveSheet();
  const dataRange = sheet.getDataRange();
  const values = dataRange.getValues();
  
  // Check if we have data
  if (values.length <= 1) {
    Logger.log('No data found or only header row present');
    return;
  }
  
  // Process each row starting from row 2 (assuming row 1 is header)
  for (let i = 1; i < values.length; i++) {
    const nomenclatureText = values[i][0]; // Column A
    
    if (!nomenclatureText || typeof nomenclatureText !== 'string') {
      continue;
    }
    
    // Extract license plates
    const extractedPlates = extractLicensePlatesFromText(nomenclatureText);
    
    // Write vehicle license plate to column B (index 1)
    if (extractedPlates.vehicle) {
      sheet.getRange(i + 1, 2).setValue(extractedPlates.vehicle);
    }
    
    // Write trailer license plate to column C (index 2)
    if (extractedPlates.trailer) {
      sheet.getRange(i + 1, 3).setValue(extractedPlates.trailer);
    }
  }
  
  Logger.log('License plate extraction completed');
}

/**
 * Extract license plate numbers from text
 * @param {string} text - The text to search for license plates
 * @return {Object} Object containing vehicle and trailer license plates
 */
function extractLicensePlatesFromText(text) {
  const result = {
    vehicle: null,
    trailer: null
  };
  
  // Ukrainian license plate pattern: 2 letters + 4 digits + 2 letters
  // Examples: AE5887КІ, СВ6395ВХ, АЕ0234ХК, СВ2061ХО
  const licensePlatePattern = /[А-ЯІЇЄҐA-Z]{2}\d{4}[А-ЯІЇЄҐA-Z]{2}/g;
  
  // Find all license plates in the text
  const allPlates = text.match(licensePlatePattern) || [];
  
  if (allPlates.length === 0) {
    return result;
  }
  
  // Context patterns to help identify vehicle vs trailer plates
  const vehicleContexts = [
    /а\/м\s+[А-ЯІЇЄҐA-Z\s]*([А-ЯІЇЄҐA-Z]{2}\d{4}[А-ЯІЇЄҐA-Z]{2})/gi,
    /автомобіль\s+[А-ЯІЇЄҐA-Z\s]*([А-ЯІЇЄҐA-Z]{2}\d{4}[А-ЯІЇЄҐA-Z]{2})/gi,
    /RENAULT\s+([А-ЯІЇЄҐA-Z]{2}\d{4}[А-ЯІЇЄҐA-Z]{2})/gi,
    /VOLVO\s+([А-ЯІЇЄҐA-Z]{2}\d{4}[А-ЯІЇЄҐA-Z]{2})/gi,
    /SCANIA\s+([А-ЯІЇЄҐA-Z]{2}\d{4}[А-ЯІЇЄҐA-Z]{2})/gi,
    /MAN\s+([А-ЯІЇЄҐA-Z]{2}\d{4}[А-ЯІЇЄҐA-Z]{2})/gi
  ];
  
  const trailerContexts = [
    /н\/пр\.?\s*([А-ЯІЇЄҐA-Z]{2}\d{4}[А-ЯІЇЄҐA-Z]{2})/gi,
    /напівпричіп\s+([А-ЯІЇЄҐA-Z]{2}\d{4}[А-ЯІЇЄҐA-Z]{2})/gi,
    /причіп\s+([А-ЯІЇЄҐA-Z]{2}\d{4}[А-ЯІЇЄҐA-Z]{2})/gi
  ];
  
  // Try to identify vehicle plates using context
  for (const pattern of vehicleContexts) {
    const match = pattern.exec(text);
    if (match && match[1]) {
      result.vehicle = match[1];
      break;
    }
  }
  
  // Try to identify trailer plates using context
  for (const pattern of trailerContexts) {
    const match = pattern.exec(text);
    if (match && match[1]) {
      result.trailer = match[1];
      break;
    }
  }
  
  // If we couldn't identify by context, use position-based logic
  if (!result.vehicle && !result.trailer && allPlates.length > 0) {
    // If we have multiple plates, assume first is vehicle, second is trailer
    if (allPlates.length >= 2) {
      result.vehicle = allPlates[0];
      result.trailer = allPlates[1];
    } else {
      // Single plate - try to determine by surrounding text
      const plateIndex = text.indexOf(allPlates[0]);
      const beforePlate = text.substring(Math.max(0, plateIndex - 20), plateIndex).toLowerCase();
      const afterPlate = text.substring(plateIndex + allPlates[0].length, plateIndex + allPlates[0].length + 20).toLowerCase();
      
      if (beforePlate.includes('н/пр') || beforePlate.includes('напівпричіп') || beforePlate.includes('причіп')) {
        result.trailer = allPlates[0];
      } else {
        result.vehicle = allPlates[0];
      }
    }
  }
  
  return result;
}

/**
 * Function to set up column headers
 */
function setupHeaders() {
  const sheet = SpreadsheetApp.getActiveSheet();
  
  // Set headers
  sheet.getRange(1, 1).setValue('Номенклатура товарів/послуг');
  sheet.getRange(1, 2).setValue('Номер автомобіля');
  sheet.getRange(1, 3).setValue('Номер причепа');
  
  // Format headers
  const headerRange = sheet.getRange(1, 1, 1, 3);
  headerRange.setFontWeight('bold');
  headerRange.setBackground('#e6f3ff');
  
  Logger.log('Headers set up successfully');
}

/**
 * Function to clear extracted data (columns B and C)
 */
function clearExtractedData() {
  const sheet = SpreadsheetApp.getActiveSheet();
  const lastRow = sheet.getLastRow();
  
  if (lastRow > 1) {
    // Clear columns B and C starting from row 2
    sheet.getRange(2, 2, lastRow - 1, 2).clearContent();
    Logger.log('Extracted data cleared');
  }
}

/**
 * Test function with sample data
 */
function testExtraction() {
  const testTexts = [
    "Перевезення вантажу за маршрутом Вільногірськ – Степанки, а/м RENAULT AE5887КІ, н/пр. АЕ0234ХК, водій Нежинський М. ТТН 0555 від 07.01.24р.",
    "Транспортна послуга у міжнародному сполученні Степанки (Україна) - Вільнюс (Литва) Строгий СВ6395ВХ СВ2061ХО"
  ];
  
  testTexts.forEach((text, index) => {
    Logger.log(`Test ${index + 1}: ${text}`);
    const result = extractLicensePlatesFromText(text);
    Logger.log(`Vehicle: ${result.vehicle}, Trailer: ${result.trailer}`);
    Logger.log('---');
  });
}
