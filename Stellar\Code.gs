/**
 * Google Apps Script for extracting vehicle and trailer license plate numbers
 * from Ukrainian transport documentation in Google Sheets
 */

/**
 * Main function to process the spreadsheet and extract license plate numbers
 */
function extractLicensePlates() {
  const sheet = SpreadsheetApp.getActiveSheet();
  const dataRange = sheet.getDataRange();
  const values = dataRange.getValues();
  
  // Check if we have data
  if (values.length <= 1) {
    Logger.log('No data found or only header row present');
    return;
  }
  
  // Process each row starting from row 2 (assuming row 1 is header)
  for (let i = 1; i < values.length; i++) {
    const nomenclatureText = values[i][0]; // Column A
    
    if (!nomenclatureText || typeof nomenclatureText !== 'string') {
      continue;
    }
    
    // Extract license plates
    const extractedPlates = extractLicensePlatesFromText(nomenclatureText);
    
    // Write vehicle license plate to column B (index 1)
    if (extractedPlates.vehicle) {
      sheet.getRange(i + 1, 2).setValue(extractedPlates.vehicle);
    }
    
    // Write trailer license plate to column C (index 2)
    if (extractedPlates.trailer) {
      sheet.getRange(i + 1, 3).setValue(extractedPlates.trailer);
    }
  }
  
  Logger.log('License plate extraction completed');
}

/**
 * Normalize text for better license plate detection
 * @param {string} text - The text to normalize
 * @return {string} Normalized text
 */
function normalizeTextForPlateDetection(text) {
  // Convert to uppercase for case-insensitive matching
  let normalized = text.toUpperCase();

  // Character mapping for common Cyrillic/Latin confusions
  const charMap = {
    // Cyrillic to Latin mappings
    'А': 'A', 'В': 'B', 'Е': 'E', 'К': 'K', 'М': 'M', 'Н': 'H', 'О': 'O', 'Р': 'P', 'С': 'C', 'Т': 'T', 'У': 'Y', 'Х': 'X',
    // Latin to Cyrillic mappings (for Ukrainian context)
    'A': 'А', 'B': 'В', 'E': 'Е', 'K': 'К', 'M': 'М', 'H': 'Н', 'O': 'О', 'P': 'Р', 'C': 'С', 'T': 'Т', 'Y': 'У', 'X': 'Х'
  };

  // Apply character normalization (prefer Cyrillic for Ukrainian plates)
  for (const [latin, cyrillic] of Object.entries(charMap)) {
    if (latin.length === 1 && cyrillic.length === 1 && latin.charCodeAt(0) < 128) {
      normalized = normalized.replace(new RegExp(latin, 'g'), cyrillic);
    }
  }

  return normalized;
}

/**
 * Extract license plate numbers from text
 * @param {string} text - The text to search for license plates
 * @return {Object} Object containing vehicle and trailer license plates
 */
function extractLicensePlatesFromText(text) {
  const result = {
    vehicle: null,
    trailer: null
  };

  // Normalize the text for better detection
  const normalizedText = normalizeTextForPlateDetection(text);

  // Enhanced Ukrainian license plate patterns
  // Pattern 1: Standard format (no spaces)
  const standardPattern = /[А-ЯІЇЄҐA-Z]{2}\d{4}[А-ЯІЇЄҐA-Z]{2}/gi;

  // Pattern 2: Format with spaces between characters
  const spacedPattern = /[А-ЯІЇЄҐA-Z]\s*[А-ЯІЇЄҐA-Z]\s*\d\s*\d\s*\d\s*\d\s*[А-ЯІЇЄҐA-Z]\s*[А-ЯІЇЄҐA-Z]/gi;

  // Pattern 3: Mixed format with optional spaces and mixed case
  const flexiblePattern = /[А-ЯІЇЄҐa-zA-Z]\s*[А-ЯІЇЄҐa-zA-Z]\s*\d\s*\d\s*\d\s*\d\s*[А-ЯІЇЄҐa-zA-Z]\s*[А-ЯІЇЄҐa-zA-Z]/gi;

  // Find all license plates using different patterns
  let allPlates = [];

  // Try standard pattern first on normalized text
  const standardMatches = normalizedText.match(standardPattern) || [];
  allPlates = allPlates.concat(standardMatches);

  // Try spaced pattern on original text (to preserve original spacing)
  const spacedMatches = text.match(spacedPattern) || [];
  const normalizedSpacedMatches = spacedMatches.map(plate =>
    normalizeTextForPlateDetection(plate.replace(/\s+/g, ''))
  );
  allPlates = allPlates.concat(normalizedSpacedMatches);

  // Try flexible pattern on original text
  const flexibleMatches = text.match(flexiblePattern) || [];
  const normalizedFlexibleMatches = flexibleMatches.map(plate =>
    normalizeTextForPlateDetection(plate.replace(/\s+/g, ''))
  );
  allPlates = allPlates.concat(normalizedFlexibleMatches);

  // Remove duplicates and filter valid plates
  allPlates = [...new Set(allPlates)].filter(plate => {
    // Ensure plate matches the exact Ukrainian format after normalization
    return /^[А-ЯІЇЄҐ]{2}\d{4}[А-ЯІЇЄҐ]{2}$/.test(plate);
  });

  if (allPlates.length === 0) {
    return result;
  }
  
  // Context patterns to help identify vehicle vs trailer plates
  const vehicleContexts = [
    /а\/м\s+[А-ЯІЇЄҐA-Z\s]*([А-ЯІЇЄҐA-Z]{2}\d{4}[А-ЯІЇЄҐA-Z]{2})/gi,
    /автомобіль\s+[А-ЯІЇЄҐA-Z\s]*([А-ЯІЇЄҐA-Z]{2}\d{4}[А-ЯІЇЄҐA-Z]{2})/gi,
    /RENAULT\s+([А-ЯІЇЄҐA-Z]{2}\d{4}[А-ЯІЇЄҐA-Z]{2})/gi,
    /VOLVO\s+([А-ЯІЇЄҐA-Z]{2}\d{4}[А-ЯІЇЄҐA-Z]{2})/gi,
    /SCANIA\s+([А-ЯІЇЄҐA-Z]{2}\d{4}[А-ЯІЇЄҐA-Z]{2})/gi,
    /MAN\s+([А-ЯІЇЄҐA-Z]{2}\d{4}[А-ЯІЇЄҐA-Z]{2})/gi
  ];

  const trailerContexts = [
    /н\/пр\.?\s*([А-ЯІЇЄҐA-Z]{2}\d{4}[А-ЯІЇЄҐA-Z]{2})/gi,
    /напівпричіп\s+([А-ЯІЇЄҐA-Z]{2}\d{4}[А-ЯІЇЄҐA-Z]{2})/gi,
    /причіп\s+([А-ЯІЇЄҐA-Z]{2}\d{4}[А-ЯІЇЄҐA-Z]{2})/gi
  ];
  
  // Try to identify vehicle plates using context
  for (const pattern of vehicleContexts) {
    const match = pattern.exec(text);
    if (match && match[1]) {
      result.vehicle = normalizeTextForPlateDetection(match[1]);
      break;
    }
  }

  // Try to identify trailer plates using context
  for (const pattern of trailerContexts) {
    const match = pattern.exec(text);
    if (match && match[1]) {
      result.trailer = normalizeTextForPlateDetection(match[1]);
      break;
    }
  }
  
  // If we couldn't identify by context, use position-based logic
  if (!result.vehicle && !result.trailer && allPlates.length > 0) {
    // If we have multiple plates, assume first is vehicle, second is trailer
    if (allPlates.length >= 2) {
      result.vehicle = allPlates[0];
      result.trailer = allPlates[1];
    } else {
      // Single plate - try to determine by surrounding text
      const plateIndex = text.indexOf(allPlates[0]);
      const beforePlate = text.substring(Math.max(0, plateIndex - 20), plateIndex).toLowerCase();
      const afterPlate = text.substring(plateIndex + allPlates[0].length, plateIndex + allPlates[0].length + 20).toLowerCase();
      
      if (beforePlate.includes('н/пр') || beforePlate.includes('напівпричіп') || beforePlate.includes('причіп')) {
        result.trailer = allPlates[0];
      } else {
        result.vehicle = allPlates[0];
      }
    }
  }
  
  return result;
}

/**
 * Function to set up column headers
 */
function setupHeaders() {
  const sheet = SpreadsheetApp.getActiveSheet();
  
  // Set headers
  sheet.getRange(1, 1).setValue('Номенклатура товарів/послуг');
  sheet.getRange(1, 2).setValue('Номер автомобіля');
  sheet.getRange(1, 3).setValue('Номер причепа');
  
  // Format headers
  const headerRange = sheet.getRange(1, 1, 1, 3);
  headerRange.setFontWeight('bold');
  headerRange.setBackground('#e6f3ff');
  
  Logger.log('Headers set up successfully');
}

/**
 * Function to clear extracted data (columns B and C)
 */
function clearExtractedData() {
  const sheet = SpreadsheetApp.getActiveSheet();
  const lastRow = sheet.getLastRow();
  
  if (lastRow > 1) {
    // Clear columns B and C starting from row 2
    sheet.getRange(2, 2, lastRow - 1, 2).clearContent();
    Logger.log('Extracted data cleared');
  }
}

/**
 * Test function with sample data including edge cases
 */
function testExtraction() {
  const testTexts = [
    // Original test cases
    "Перевезення вантажу за маршрутом Вільногірськ – Степанки, а/м RENAULT AE5887КІ, н/пр. АЕ0234ХК, водій Нежинський М. ТТН 0555 від 07.01.24р.",
    "Транспортна послуга у міжнародному сполученні Степанки (Україна) - Вільнюс (Литва) Строгий СВ6395ВХ СВ2061ХО",

    // Edge case: The specific missing plate
    "Транспортування вантажу автомобілем СА9075ВН",

    // Edge case: Lowercase letters
    "а/м VOLVO са9075вн, н/пр. ае0234хк",

    // Edge case: Spaced letters
    "Автомобіль С А 9075 В Н, причіп А Е 0234 Х К",

    // Edge case: Mixed Cyrillic/Latin
    "а/м SCANIA CA9075BH, н/пр. AE0234XK",

    // Edge case: Various spacing
    "Перевезення а/м  С А9075 ВН  н/пр.  А Е0234ХК",

    // Edge case: Mixed case with spaces
    "транспорт с а 9075 в н та а е 0234 х к"
  ];

  testTexts.forEach((text, index) => {
    Logger.log(`Test ${index + 1}: ${text}`);
    const result = extractLicensePlatesFromText(text);
    Logger.log(`Vehicle: ${result.vehicle}, Trailer: ${result.trailer}`);
    Logger.log('---');
  });
}
