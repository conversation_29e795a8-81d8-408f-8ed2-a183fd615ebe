# Project Brief

**Core Requirements:**
- Create a Google Apps Script project.
- Extract vehicle and trailer license plate numbers from Google Sheets.
- Process data in column A titled "Номенклатура товарів/послуг".
- Extract vehicle license plate numbers (e.g., "AE5887КІ", "СВ6395ВХ").
- Extract trailer license plate numbers (e.g., "АЕ0234ХК", "СВ2061ХО").
- Write extracted vehicle license plate numbers to column B.
- Write extracted trailer license plate numbers to column C.
- Handle various text formats, including case sensitivity, Cyrillic/Latin equivalents, and spaces within license plates.

**Goals:**
- Develop a robust Google Apps Script for license plate extraction.
- Ensure accurate identification and separation of vehicle and trailer plates.
- Provide a user-friendly interface within Google Sheets to run the script.
