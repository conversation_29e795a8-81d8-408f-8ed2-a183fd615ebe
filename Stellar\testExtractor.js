/**
 * Тестування українського екстрактора номерних знаків
 */

// Імпорт для Node.js
let LicensePlateExtractor;
if (typeof require !== 'undefined') {
  try {
    LicensePlateExtractor = require('./licensePlateExtractor.js');
  } catch (e) {
    console.log('Запуск в браузері');
  }
}

// Використання відповідного посилання залежно від середовища
const { extractLicensePlatesFromText } = LicensePlateExtractor || window.LicensePlateExtractor || {};

/**
 * Тестові випадки
 */
const testCases = [
  {
    name: "Стандартний формат з контекстом",
    text: "Перевезення вантажу за маршрутом Вільногірськ – Степанки, а/м RENAULT AE5887КІ, н/пр. АЕ0234ХК, водій Нежинський М. ТТН 0555 від 07.01.24р.",
    expected: { vehicle: "AE5887КІ", trailer: "АЕ0234ХК" },
    description: "Англійські букви в першому номері залишаються, українські в другому"
  },
  {
    name: "Кілька номерів без контексту",
    text: "Транспортна послуга у міжнародному сполученні Степанки (Україна) - Вільнюс (Литва) Строгий СВ6395ВХ СВ2061ХО",
    expected: { vehicle: "СВ6395ВХ", trailer: "СВ2061ХО" },
    description: "Перший номер - автомобіль, другий - причіп"
  },
  {
    name: "Один номер автомобіля",
    text: "Транспортування вантажу автомобілем СА9075ВН",
    expected: { vehicle: "СА9075ВН", trailer: null },
    description: "Тільки один номер - автомобіль"
  },
  {
    name: "Малі літери",
    text: "а/м VOLVO са9075вн, н/пр. ае0234хк",
    expected: { vehicle: "СА9075ВН", trailer: "АЕ0234ХК" },
    description: "Конвертація малих літер у великі та латиниці в кирилицю"
  },
  {
    name: "Літери з пробілами",
    text: "Автомобіль С А 9075 В Н, причіп А Е 0234 Х К",
    expected: { vehicle: "СА9075ВН", trailer: "АЕ0234ХК" },
    description: "Видалення пробілів між символами"
  },
  {
    name: "Змішані кирилиця/латиниця",
    text: "а/м SCANIA CA9075BH, н/пр. AE0234XK",
    expected: { vehicle: "CA9075BH", trailer: "AE0234XK" },
    description: "Англійські букви залишаються як є"
  },
  {
    name: "Формат з пробілами та слешем",
    text: "Транспортні послуги смт. Малинівка - с. Степанки , РЕНО ВН 6524 НР / ВО 1518 XF",
    expected: { vehicle: "ВН6524НР", trailer: "ВО1518XF" },
    description: "Перший номер українські букви, другий з англійськими"
  },
  {
    name: "Тест хибного спрацьовування - ТТН",
    text: "Транспортні послуги за маршрутом м.Вільногірськ - с.Степанки ттн 1037 від 29.02.2024",
    expected: { vehicle: null, trailer: null },
    description: "Не повинен витягувати номери з документів"
  },
  {
    name: "Українська назва бренду",
    text: "РЕНО ВН6524НР перевезення вантажу",
    expected: { vehicle: "ВН6524НР", trailer: null },
    description: "Один номер з українськими буквами"
  },
  {
    name: "Англійські букви в номері",
    text: "VOLVO AB1234CD транспортування",
    expected: { vehicle: "AB1234CD", trailer: null },
    description: "Англійські букви залишаються без змін"
  }
];

/**
 * Запуск одного тесту
 */
function runTestCase(testCase, index) {
  console.log(`\n--- Тест ${index + 1}: ${testCase.name} ---`);
  console.log(`Вхід: "${testCase.text}"`);
  console.log(`Опис: ${testCase.description}`);
  
  const result = extractLicensePlatesFromText(testCase.text);
  
  console.log(`Очікується: Автомобіль: ${testCase.expected.vehicle}, Причіп: ${testCase.expected.trailer}`);
  console.log(`Результат:  Автомобіль: ${result.vehicle}, Причіп: ${result.trailer}`);
  
  const vehicleMatch = result.vehicle === testCase.expected.vehicle;
  const trailerMatch = result.trailer === testCase.expected.trailer;
  const passed = vehicleMatch && trailerMatch;
  
  console.log(`Статус: ${passed ? '✅ ПРОЙШОВ' : '❌ НЕ ПРОЙШОВ'}`);
  
  if (!passed) {
    if (!vehicleMatch) {
      console.log(`  Автомобіль не співпадає: очікувалось "${testCase.expected.vehicle}", отримано "${result.vehicle}"`);
    }
    if (!trailerMatch) {
      console.log(`  Причіп не співпадає: очікувалось "${testCase.expected.trailer}", отримано "${result.trailer}"`);
    }
  }
  
  return passed;
}

/**
 * Запуск всіх тестів
 */
function runAllTests() {
  console.log('🚀 Запуск тестів українського екстрактора номерних знаків');
  console.log('=' .repeat(70));
  
  if (!extractLicensePlatesFromText) {
    console.error('❌ Помилка: функція extractLicensePlatesFromText не знайдена!');
    console.log('Переконайтеся, що licensePlateExtractor.js завантажено');
    return;
  }
  
  let passed = 0;
  let failed = 0;
  
  testCases.forEach((testCase, index) => {
    if (runTestCase(testCase, index)) {
      passed++;
    } else {
      failed++;
    }
  });
  
  console.log('\n' + '=' .repeat(70));
  console.log(`📊 Результати тестів: ${passed} пройшли, ${failed} не пройшли з ${testCases.length} загалом`);
  
  if (failed === 0) {
    console.log('🎉 Всі тести пройшли успішно!');
  } else {
    console.log(`⚠️  ${failed} тест(ів) не пройшли. Перевірте реалізацію.`);
  }
  
  return { passed, failed, total: testCases.length };
}

/**
 * Інтерактивна функція для ручного тестування
 */
function testSingleText(text) {
  console.log(`\n🔍 Тестування: "${text}"`);
  const result = extractLicensePlatesFromText(text);
  console.log(`Результат: Автомобіль: ${result.vehicle}, Причіп: ${result.trailer}`);
  return result;
}

// Експорт функцій
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    runAllTests,
    testSingleText,
    testCases
  };
} else if (typeof window !== 'undefined') {
  window.LicensePlateExtractorTests = {
    runAllTests,
    testSingleText,
    testCases
  };
}

// Автозапуск тестів якщо файл виконується безпосередньо
if (typeof require !== 'undefined' && require.main === module) {
  runAllTests();
}
