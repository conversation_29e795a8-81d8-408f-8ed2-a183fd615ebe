# Ukrainian License Plate Extractor for Google Sheets

This Google Apps Script project extracts vehicle and trailer license plate numbers from Ukrainian transport documentation stored in Google Sheets.

## Features

- Extracts Ukrainian license plate numbers (format: 2 letters + 4 digits + 2 letters)
- Distinguishes between vehicle and trailer license plates using context analysis
- Processes data from column A and outputs to columns B (vehicle) and C (trailer)
- Handles various text formats and contexts

## Setup Instructions

1. **Create a new Google Apps Script project:**
   - Go to [script.google.com](https://script.google.com)
   - Click "New Project"
   - Replace the default code with the contents of `Code.gs`
   - Add the `appsscript.json` configuration

2. **Prepare your Google Sheets document:**
   - Column A should contain the transport documentation text
   - The script will write vehicle plates to column B
   - The script will write trailer plates to column C

3. **Run the setup:**
   - Execute the `setupHeaders()` function to create proper column headers
   - This will set up: "Номенклатура товарів/послуг", "Номер автомобіля", "Номер причепа"

## Usage

### Main Functions

- **`extractLicensePlates()`** - Main function to process all data in the active sheet
- **`setupHeaders()`** - Sets up column headers with proper formatting
- **`clearExtractedData()`** - Clears previously extracted data from columns B and C
- **`testExtraction()`** - Tests the extraction logic with sample data

### Running the Script

1. Open your Google Sheets document with transport data
2. Go to Extensions → Apps Script
3. Paste the code and save
4. Run `setupHeaders()` first (if needed)
5. Run `extractLicensePlates()` to process all data

## Supported Text Formats

The script can handle various Ukrainian transport documentation formats and edge cases:

### Vehicle License Plate Contexts
- `а/м RENAULT AE5887КІ` (automobile + brand + plate)
- `автомобіль VOLVO СВ6395ВХ` (automobile + brand + plate)
- Brand names: RENAULT, VOLVO, SCANIA, MAN, etc.

### Trailer License Plate Contexts
- `н/пр. АЕ0234ХК` (semi-trailer abbreviation + plate)
- `напівпричіп СВ2061ХО` (semi-trailer + plate)
- `причіп АЕ0234ХК` (trailer + plate)

### Edge Cases Handled
- **Case insensitive**: `са9075вн` → `СА9075ВН`
- **Spaced characters**: `С А 9075 В Н` → `СА9075ВН`
- **Mixed Cyrillic/Latin**: `CA9075BH` → `СА9075ВН`
- **Various spacing**: `С А9075 ВН` → `СА9075ВН`
- **Character normalization**: Automatically converts between similar Cyrillic and Latin characters

### Example Input Texts

```
Перевезення вантажу за маршрутом Вільногірськ – Степанки, а/м RENAULT AE5887КІ, н/пр. АЕ0234ХК, водій Нежинський М. ТТН 0555 від 07.01.24р.
```
**Output:** Vehicle: `AE5887КІ`, Trailer: `АЕ0234ХК`

```
Транспортна послуга у міжнародному сполученні Степанки (Україна) - Вільнюс (Литва) Строгий СВ6395ВХ СВ2061ХО
```
**Output:** Vehicle: `СВ6395ВХ`, Trailer: `СВ2061ХО`

```
Транспортування вантажу автомобілем СА9075ВН
```
**Output:** Vehicle: `СА9075ВН`, Trailer: `null`

```
а/м VOLVO са9075вн, н/пр. ае0234хк
```
**Output:** Vehicle: `СА9075ВН`, Trailer: `АЕ0234ХК`

```
Автомобіль С А 9075 В Н, причіп А Е 0234 Х К
```
**Output:** Vehicle: `СА9075ВН`, Trailer: `АЕ0234ХК`

## License Plate Format

Ukrainian license plates follow the format: **XX0000XX**
- 2 Cyrillic or Latin letters
- 4 digits
- 2 Cyrillic or Latin letters

Examples: `AE5887КІ`, `СВ6395ВХ`, `АЕ0234ХК`, `СВ2061ХО`

## Troubleshooting

1. **No data extracted:** Check that column A contains text with license plate numbers
2. **Wrong classification:** The script uses context clues; manual review may be needed for ambiguous cases
3. **Missing plates:** Ensure license plates follow the Ukrainian format (XX0000XX)

## Customization

You can modify the script to:
- Add support for other license plate formats
- Include additional vehicle brands in the context patterns
- Adjust the extraction logic for specific document formats
- Add validation rules for extracted plates

## Permissions

The script requires permission to:
- Read and modify your Google Sheets
- Access the Google Sheets API

These permissions are requested when you first run the script.
