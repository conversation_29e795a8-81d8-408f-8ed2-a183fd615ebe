# Tech Context

**Technologies Used:**
- **Google Apps Script (JavaScript):** The primary development platform and language for this project, providing direct integration with Google Workspace services.
- **Google Sheets API:** Used implicitly by Google Apps Script to interact with spreadsheet data (reading cell values, writing to cells, creating menus).
- **Regular Expressions (Regex):** Fundamental for pattern matching and extracting specific data (license plate numbers) from text strings.

**Development Setup:**
- Development is done within the Google Apps Script editor (script.google.com) or via CLASP (Command Line Interface for Apps Script projects). For this task, the script is provided as a `.js` file, which would typically be copied and pasted into a new Google Apps Script project.
- No local development server or complex build process is required.

**Technical Constraints:**
- **Google Apps Script Quotas:** Operations are subject to daily quotas (e.g., maximum number of API calls, execution time). Batch processing (`setValues`) is used to mitigate this.
- **Execution Environment:** The script runs in Google's cloud environment, not locally on the user's machine.
- **Limited Debugging:** Debugging tools are available within the Google Apps Script editor, but are less robust than traditional IDEs.
- **Spreadsheet Structure:** Assumes a consistent structure where "Номенклатура товарів/послуг" is in column A, and columns B and C are available for output.

**Dependencies:**
- No external libraries or npm packages are directly used within the Google Apps Script environment. All functionalities are provided by the built-in Apps Script services.

**Tool Usage Patterns:**
- **`SpreadsheetApp`:** Used to access the active spreadsheet and its sheets.
- **`Range.getValues()` and `Range.setValues()`:** Used for efficient reading and writing of data to and from the spreadsheet.
- **`String.prototype.matchAll()`:** Used for finding all occurrences of the license plate regex pattern.
- **`String.prototype.match()`:** Used for finding specific matches with contextual indicators.
- **`RegExp` objects:** Used for defining and applying complex pattern matching.
- **`SpreadsheetApp.getUi().createMenu().addItem().addToUi()`:** Used to create a custom menu for user interaction.
